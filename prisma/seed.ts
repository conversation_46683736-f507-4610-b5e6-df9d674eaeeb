import { PrismaClient, Prisma, EntityStatus, TechnicalLevel, PricingModel, SkillLevel, UserRole, UserStatus } from '../generated/prisma';
import type { EntityType, Category, Tag, Feature } from '../generated/prisma';

const prisma = new PrismaClient();

async function main() {
  console.log(`Start seeding ...`);

  // --- Upsert a default submitter user ---
  const defaultUserEmail = '<EMAIL>';
  const defaultAuthUserId = '00000000-0000-0000-0000-000000000000'; // A stable, placeholder UUID for the seed user

  const submitter = await prisma.user.upsert({
    where: { email: defaultUserEmail },
    update: {},
    create: {
      id: '00000000-0000-0000-0000-000000000001', // Stable UUID
      authUserId: defaultAuthUserId,
      email: defaultUserEmail,
      displayName: 'Seed Submitter',
      role: UserRole.ADMIN,
      status: UserStatus.ACTIVE,
    },
  });
  console.log(`Upserted submitter: ${submitter.email} (ID: ${submitter.id}, AuthUID: ${submitter.authUserId})`);

  // --- Seed App Settings ---
  console.log('Seeding App Settings...');

  // Create the app_settings table if it doesn't exist
  try {
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "public"."app_settings" (
        "key" TEXT NOT NULL,
        "value" TEXT NOT NULL,
        "description" TEXT,
        "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updated_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT "app_settings_pkey" PRIMARY KEY ("key")
      );
    `;
    console.log('App settings table created or already exists');
  } catch (error) {
    console.log('App settings table creation skipped (may already exist):', error.message);
  }

  // Upsert the LLM provider setting
  await prisma.appSetting.upsert({
    where: { key: 'CURRENT_LLM_PROVIDER' },
    update: {},
    create: {
      key: 'CURRENT_LLM_PROVIDER',
      value: 'OPENAI',
      description: 'The LLM provider to use for recommendations. Options: OPENAI, GOOGLE_GEMINI, ANTHROPIC'
    },
  });
  console.log('Upserted LLM provider setting: OPENAI');

  // --- Seed Entity Types ---
  console.log('Seeding EntityTypes...');
  const entityTypesData = [
    { id: '10000000-0000-0000-0000-000000000001', name: 'AI Tool', slug: 'ai-tool', description: 'Software, SaaS, APIs, Models, Libraries, Frameworks for AI.' },
    { id: '10000000-0000-0000-0000-000000000002', name: 'Course', slug: 'course', description: 'Educational courses, tutorials, and learning resources.' },
    { id: '10000000-0000-0000-0000-000000000003', name: 'Dataset', slug: 'dataset', description: 'Collections of data for training or analysis.' },
    { id: '10000000-0000-0000-0000-000000000004', name: 'Research Paper', slug: 'research-paper', description: 'Academic papers and research articles.' },
    { id: '10000000-0000-0000-0000-000000000005', name: 'Platform', slug: 'platform', description: 'Platforms providing AI services or infrastructure.' },
    { id: '10000000-0000-0000-0000-000000000006', name: 'Hardware', slug: 'hardware', description: 'Physical hardware for AI computation.' },
    { id: '10000000-0000-0000-0000-000000000007', name: 'Newsletter', slug: 'newsletter', description: 'Regular publications on AI topics.' },
    { id: '10000000-0000-0000-0000-000000000008', name: 'Community', slug: 'community', description: 'Online or offline AI communities.' },
    { id: '10000000-0000-0000-0000-000000000009', name: 'Event', slug: 'event', description: 'Conferences, workshops, and meetups.' },
  ];
  const entityTypes: Record<string, EntityType> = {};
  for (const etData of entityTypesData) {
    const entityType = await prisma.entityType.upsert({
      where: { slug: etData.slug }, // Slug is unique for EntityType
      update: { name: etData.name, description: etData.description },
      create: etData, // id is provided in etData
    });
    entityTypes[etData.slug] = entityType;
    console.log(`Upserted EntityType: ${entityType.name}`);
  }

  // --- Seed Categories ---
  console.log('Seeding Categories...');
  const categoryData = [
    // Top Level
    { name: 'Content & Media', slug: 'content-media', description: 'Tools for generating and manipulating text, images, video, and audio content.' },
    { name: 'Software & Code Development', slug: 'software-code-development', description: 'Tools, libraries, and platforms for building and testing software.' },
    { name: 'Data & Analytics', slug: 'data-analytics', description: 'Platforms for data analysis, business intelligence, and machine learning.' },
    { name: 'Business & Productivity', slug: 'business-productivity', description: 'AI-powered tools to enhance business operations and personal productivity.' },
    { name: 'Education & Research', slug: 'education-research', description: 'Resources for learning, teaching, and conducting research in AI.' },
    { name: 'Hardware & Infrastructure', slug: 'hardware-infrastructure', description: 'Physical hardware and cloud infrastructure for running AI workloads.' },
    { name: 'Community & Information', slug: 'community-information', description: 'Platforms for news, discussion, and community engagement around AI.' },
    // Children
    { name: 'Text Generation', slug: 'text-generation', description: 'AI that writes, from marketing copy and emails to creative stories.', parentSlug: 'content-media' },
    { name: 'Image Generation', slug: 'image-generation', description: 'Create stunning visuals and art from text prompts or other images.', parentSlug: 'content-media' },
    { name: 'Video Generation', slug: 'video-generation', description: 'Tools for creating and editing video content using AI.', parentSlug: 'content-media' },
    { name: 'Audio & Voice', slug: 'audio-voice', description: 'Includes text-to-speech, voice cloning, and AI music generation.', parentSlug: 'content-media' },
    { name: 'Design & Art', slug: 'design-art', description: 'AI assistants for graphic design, UI/UX mockups, and artistic creation.', parentSlug: 'content-media' },
    { name: 'Marketing & SEO', slug: 'marketing-seo', description: 'Optimize marketing campaigns and SEO with AI-driven insights.', parentSlug: 'content-media' },
    { name: 'Developer Tools', slug: 'developer-tools', description: 'AI code assistants, testing tools, and development environments.', parentSlug: 'software-code-development' },
    { name: 'Low-Code/No-Code', slug: 'low-code-no-code', description: 'Build applications and workflows with minimal to no coding required.', parentSlug: 'software-code-development' },
    { name: 'Testing & QA', slug: 'testing-qa', description: 'Automate software testing and quality assurance with AI.', parentSlug: 'software-code-development' },
    { name: 'DevOps & MLOps', slug: 'devops-mlops', description: 'Tools for managing the lifecycle of machine learning models and infrastructure.', parentSlug: 'software-code-development' },
    { name: 'Data Science & ML Platforms', slug: 'data-science-ml-platforms', description: 'Comprehensive platforms for the entire data science and machine learning workflow.', parentSlug: 'data-analytics' },
    { name: 'Business Intelligence', slug: 'business-intelligence', description: 'Analyze business data and generate actionable insights and reports.', parentSlug: 'data-analytics' },
    { name: 'Data Annotation & Labeling', slug: 'data-annotation-labeling', description: 'Services and tools for labeling data to train machine learning models.', parentSlug: 'data-analytics' },
    { name: 'Databases & Vector Search', slug: 'databases-vector-search', description: 'Specialized databases for handling vector embeddings and enabling semantic search.', parentSlug: 'data-analytics' },
    { name: 'Productivity', slug: 'productivity', description: 'Enhance your daily workflow with AI-powered assistants, note-takers, and schedulers.', parentSlug: 'business-productivity' },
    { name: 'Customer Support', slug: 'customer-support', description: 'AI chatbots and platforms to automate and improve customer service.', parentSlug: 'business-productivity' },
    { name: 'Sales & CRM', slug: 'sales-crm', description: 'Tools for lead generation, sales automation, and customer relationship management.', parentSlug: 'business-productivity' },
    { name: 'Human Resources', slug: 'human-resources', description: 'AI tools for recruiting, employee onboarding, and HR management.', parentSlug: 'business-productivity' },
    { name: 'Legal & Compliance', slug: 'legal-compliance', description: 'AI for legal research, contract analysis, and compliance monitoring.', parentSlug: 'business-productivity' },
    { name: 'Finance & Accounting', slug: 'finance-accounting', description: 'Automate accounting, financial analysis, and expense tracking.', parentSlug: 'business-productivity' },
  ];

  const createdCategories: Record<string, { id: string }> = {};
  for (const cat of categoryData.filter(c => !c.parentSlug)) {
    const newCat = await prisma.category.upsert({
      where: { slug: cat.slug },
      update: { name: cat.name, description: cat.description },
      create: { name: cat.name, slug: cat.slug, description: cat.description },
    });
    createdCategories[cat.slug] = newCat;
    console.log(`Upserted top-level category: ${newCat.name}`);
  }

  for (const cat of categoryData.filter(c => c.parentSlug)) {
    const parent = createdCategories[cat.parentSlug];
    if (parent) {
      const newCat = await prisma.category.upsert({
        where: { slug: cat.slug },
        update: { name: cat.name, description: cat.description, parentId: parent.id },
        create: { name: cat.name, slug: cat.slug, description: cat.description, parentId: parent.id },
      });
      console.log(`Upserted sub-category: ${newCat.name}`);
    } else {
      console.warn(`Could not find parent category with slug: ${cat.parentSlug} for sub-category: ${cat.name}`);
    }
  }

  // Create categories lookup for entity seeding
  const categories: Record<string, Category> = {};
  const allCategories = await prisma.category.findMany();
  for (const category of allCategories) {
    categories[category.slug] = category;
  }

  // --- Seed Tags ---
  console.log('Seeding Tags...');
  const tagsData = [
    { id: '30000000-0000-0000-0000-000000000001', name: 'API Access', slug: 'api-access', description: 'Provides API access.' },
    { id: '30000000-0000-0000-0000-000000000002', name: 'Open Source', slug: 'open-source', description: 'Open source projects.' },
    { id: '30000000-0000-0000-0000-000000000003', name: 'Free Tier', slug: 'free-tier', description: 'Offers a free tier.' },
    { id: '30000000-0000-0000-0000-000000000004', name: 'LLM', slug: 'llm', description: 'Based on Large Language Models.' },
    { id: '30000000-0000-0000-0000-000000000005', name: 'Python', slug: 'python', description: 'Related to Python.' },
    { id: '30000000-0000-0000-0000-000000000006', name: 'JavaScript', slug: 'javascript', description: 'Related to JavaScript.' },
    { id: '30000000-0000-0000-0000-000000000007', name: 'TensorFlow', slug: 'tensorflow', description: 'Uses TensorFlow.' },
    { id: '30000000-0000-0000-0000-000000000008', name: 'PyTorch', slug: 'pytorch', description: 'Uses PyTorch.' },
    { id: '30000000-0000-0000-0000-000000000009', name: 'Tutorial', slug: 'tutorial', description: 'Educational tutorial format.' },
    { id: '30000000-0000-0000-0000-000000000010', name: 'Generative AI', slug: 'generative-ai', description: 'Focuses on generative AI.' },
    { id: '30000000-0000-0000-0000-000000000011', name: 'Cloud-Based', slug: 'cloud-based', description: 'Runs on cloud infrastructure.' },
    { id: '30000000-0000-0000-0000-000000000012', name: 'MLOps', slug: 'mlops', description: 'Related to MLOps practices.' },
    { id: '30000000-0000-0000-0000-000000000013', name: 'Data Visualization', slug: 'data-visualization', description: 'Tools for visualizing data.' },
    { id: '30000000-0000-0000-0000-000000000014', name: 'Research', slug: 'research', description: 'Focus on AI research.' },
    { id: '30000000-0000-0000-0000-000000000015', name: 'Beginner-Friendly', slug: 'beginner-friendly', description: 'Suitable for beginners.' },
  ];
  const tags: Record<string, Tag> = {};
  for (const tagData of tagsData) {
    const tag = await prisma.tag.upsert({
      where: { slug: tagData.slug }, // Slug is unique for Tag
      update: { name: tagData.name, description: tagData.description },
      create: tagData, // id is provided in tagData
    });
    tags[tagData.slug] = tag;
    console.log(`Upserted Tag: ${tag.name}`);
  }

  // --- Seed Features ---
  console.log('Seeding Features...');
  const featureData = [
    { name: 'API Access', slug: 'api-access', description: 'Allows programmatic integration with other applications via an API.' },
    { name: 'Text Summarization', slug: 'text-summarization', description: 'Condenses long-form text into concise, easy-to-read summaries.' },
    { name: 'Language Translation', slug: 'language-translation', description: 'Translates text from one language to another.' },
    { name: 'Sentiment Analysis', slug: 'sentiment-analysis', description: 'Determines the emotional tone (positive, negative, neutral) of a piece of text.' },
    { name: 'Question Answering', slug: 'question-answering', description: 'Provides direct answers to questions based on a given context or knowledge base.' },
    { name: 'Speech-to-Text Transcription', slug: 'speech-to-text-transcription', description: 'Converts spoken language from audio or video into written text.' },
    { name: 'Text-to-Speech Synthesis', slug: 'text-to-speech-synthesis', description: 'Generates human-like speech from written text.' },
    { name: 'Code Generation & Completion', slug: 'code-generation-completion', description: 'Assists developers by writing, completing, or debugging code snippets in various languages.' },
    { name: 'Image Recognition & Tagging', slug: 'image-recognition-tagging', description: 'Identifies and labels objects, scenes, and concepts within images.' },
    { name: 'Predictive Analytics', slug: 'predictive-analytics', description: 'Uses statistical algorithms and machine learning to predict future outcomes based on historical data.' },
    { name: 'Collaboration Tools', slug: 'collaboration-tools', description: 'Features that allow multiple users to work together within the application in real-time.' },
    { name: 'Custom Workflows', slug: 'custom-workflows', description: 'Enables users to build and automate bespoke multi-step processes to fit their needs.' },
    { name: 'Real-time Processing', slug: 'real-time-processing', description: 'Processes data and provides results instantaneously as the data is received.' },
    { name: 'Browser Extension', slug: 'browser-extension', description: 'Offers a browser extension for seamless integration with web workflows.' },
    { name: 'SDKs Available', slug: 'sdks-available', description: 'Provides Software Development Kits for easier integration into applications (e.g., Python, JS).' },
    { name: 'Live Chat Support', slug: 'live-chat-support', description: 'Offers real-time support assistance via a chat interface.' },
    { name: 'Comprehensive Documentation', slug: 'comprehensive-documentation', description: 'Provides extensive and clear documentation for users and developers.' },
  ];

  const features: Record<string, Feature> = {};
  for (const feat of featureData) {
    const newFeat = await prisma.feature.upsert({
      where: { slug: feat.slug },
      update: { name: feat.name, description: feat.description },
      create: { name: feat.name, slug: feat.slug, description: feat.description },
    });
    features[feat.slug] = newFeat;
    console.log(`Upserted feature: ${newFeat.name}`);
  }

  // --- Seed Entities ---
  console.log('Seeding Entities...');
  if (submitter && Object.keys(entityTypes).length > 0 && Object.keys(categories).length > 0 && Object.keys(tags).length > 0 && Object.keys(features).length > 0) {
    const entitiesToSeed = [
      {
        name: 'CodePal AI',
        slug: 'codepal-ai',
        websiteUrl: 'https://codepal.example.com',
        shortDescription: 'Your AI-powered coding assistant.',
        description: 'CodePal AI helps developers write better code faster using advanced AI models and integrations.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['ai-tool'].id,
        details: {
          entityDetailsTool: {
            technicalLevel: TechnicalLevel.INTERMEDIATE,
            hasApi: true,
            pricingModel: PricingModel.FREEMIUM,
            hasFreeTier: true,
            keyFeatures: ['Real-time code completion', 'AI-powered debugging', 'Code generation'],
            useCases: ['Accelerating software development', 'Improving code quality'],
            platforms: ['VS Code Extension', 'JetBrains IDEs', 'Web App'],
          }
        },
        categoryIds: [categories['developer-tools'].id, categories['content-media'].id],
        tagIds: [tags['api-access'].id, tags['llm'].id, tags['python'].id, tags['free-tier'].id],
        featureIds: [features['api-access'].id, features['comprehensive-documentation'].id]
      },
      {
        name: 'MLFlow Advanced Course',
        slug: 'mlflow-advanced-course',
        websiteUrl: 'https://mlcourse.example.com/mlflow',
        shortDescription: 'Master MLFlow for MLOps.',
        description: 'A comprehensive course on using MLFlow for managing the machine learning lifecycle, from experimentation to deployment.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['course'].id,
        details: {
          entityDetailsCourse: {
            instructorName: 'Dr. AI Expert',
            skillLevel: SkillLevel.ADVANCED,
            durationText: '8 Weeks',
            certificateAvailable: true,
            prerequisites: 'Basic Python, ML concepts, Docker familiarity',
          }
        },
        categoryIds: [categories['data-science-ml-platforms'].id, categories['education-research'].id],
        tagIds: [tags['tutorial'].id, tags['mlops'].id, tags['python'].id ],
        featureIds: [features['comprehensive-documentation'].id, features['live-chat-support']?.id].filter(id => !!id)
      },
      // {
      //   name: 'OpenImages V7 Dataset',
      //   websiteUrl: 'https://storage.googleapis.com/openimages/web/index.html',
      //   shortDescription: 'Large-scale, multi-label image dataset.',
      //   description: 'OpenImages is a dataset of ~9M images annotated with image-level labels, object bounding boxes, object segmentation masks, visual relationships, and localized narratives.',
      //   status: EntityStatus.ACTIVE,
      //   entityTypeId: entityTypes['dataset'].id,
      //   details: {
      //     entityDetailsDataset: {
      //       format: 'Mixed (JPEG, CSV for annotations)',
      //       sourceUrl: 'https://storage.googleapis.com/openimages/web/index.html',
      //       license: 'CC BY 4.0',
      //       sizeInBytes: BigInt(10 * 1024 * 1024 * 1024), // Approx 10TB
      //       description: 'Contains bounding boxes for 600 object classes, plus image-level labels for thousands of classes.'
      //     }
      //   },
      //   categoryIds: [categories['computer-vision'].id, categories['data-science-analytics'].id],
      //   tagIds: [tags['open-source'].id, tags['research']?.id].filter(id => !!id),
      //   featureIds: []
      // },
      {
        name: 'Attention Is All You Need',
        slug: 'attention-is-all-you-need',
        websiteUrl: 'https://arxiv.org/abs/1706.03762',
        shortDescription: 'The seminal paper introducing the Transformer model.',
        description: 'This paper proposed the Transformer, a novel network architecture based solely on attention mechanisms, dispensing with recurrence and convolutions entirely.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['research-paper'].id,
        details: {
          entityDetailsResearchPaper: {
            publicationDate: new Date('2017-06-12'),
            doi: '10.48550/arXiv.1706.03762',
            authors: ['Ashish Vaswani', 'Noam Shazeer', 'Niki Parmar', 'Jakob Uszkoreit', 'Llion Jones', 'Aidan N. Gomez', 'Łukasz Kaiser', 'Illia Polosukhin'],
            journalOrConference: 'NeurIPS 2017',
            citationCount: 90000
          }
        },
        categoryIds: [categories['content-media'].id, categories['education-research'].id],
        tagIds: [tags['llm'].id, tags['generative-ai'].id, tags['research'].id],
        featureIds: []
      },
      {
        name: 'TensorFlow Extended (TFX)',
        slug: 'tensorflow-extended-tfx',
        websiteUrl: 'https://www.tensorflow.org/tfx',
        shortDescription: 'An end-to-end platform for deploying production ML pipelines.',
        description: 'TFX is a Google-production-scale machine learning platform that provides a configuration framework and shared libraries to integrate common components needed to define, launch, and monitor your machine learning system.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['platform'].id,
        details: {
          entityDetailsPlatform: {
            platformType: 'MLOps Platform',
            keyServices: ['Data Validation', 'Model Training', 'Model Serving', 'Pipeline Orchestration'],
            documentationUrl: 'https://www.tensorflow.org/tfx/guide',
            pricingModel: PricingModel.OPEN_SOURCE
          }
        },
        categoryIds: [categories['data-science-ml-platforms'].id, categories['developer-tools'].id, categories['hardware-infrastructure'].id],
        tagIds: [tags['tensorflow'].id, tags['mlops'].id, tags['open-source'].id, tags['cloud-based'].id],
        featureIds: [features['comprehensive-documentation'].id, features['api-access']?.id, features['live-chat-support']?.id].filter(id => !!id)
      },
       {
        name: 'AI Ethics Weekly Newsletter',
        slug: 'ai-ethics-weekly-newsletter',
        websiteUrl: 'https://aiethicsweekly.example.com',
        shortDescription: 'Curated newsletter on AI ethics and responsible AI.',
        description: 'A weekly roundup of news, research, and discussions on the ethical implications of artificial intelligence and how to build more responsible AI systems.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['newsletter'].id,
        details: {
          entityDetailsNewsletter: {
            frequency: 'Weekly',
            mainTopics: ['AI Bias', 'Fairness in ML', 'AI Governance', 'AI Transparency'],
            authorName: 'Ethica AI Group',
            subscriberCount: 15000,
            subscribeUrl: 'https://aiethicsweekly.example.com/subscribe'
          }
        },
        categoryIds: [categories['education-research'].id, categories['community-information'].id],
        tagIds: [tags['generative-ai']?.id, tags['research']?.id].filter(id => !!id),
        featureIds: []
      },
      {
        name: 'AI Hardware Showcase 2024',
        slug: 'ai-hardware-showcase-2024',
        websiteUrl: 'https://aihardwareshow.example.com',
        shortDescription: 'Annual event for AI hardware innovations.',
        description: 'The premier event showcasing the latest in AI chips, servers, and hardware solutions from startups and industry giants.',
        status: EntityStatus.ACTIVE,
        entityTypeId: entityTypes['event'].id,
        details: {
          entityDetailsEvent: {
            startDate: new Date('2024-10-22'),
            endDate: new Date('2024-10-24'),
            location: 'San Francisco, CA',
            isOnline: false,
            topics: ['AI Accelerators', 'Neuromorphic Computing', 'Edge AI Hardware']
          }
        },
        categoryIds: [categories['hardware-infrastructure'].id, categories['community-information'].id],
        tagIds: [tags['cloud-based']?.id, tags['research']?.id].filter(id => !!id),
        featureIds: []
      }
    ];

    for (const entityData of entitiesToSeed) {
      const { details, categoryIds, tagIds, featureIds, ...restOfEntityData } = entityData;

      // 1. Upsert the base entity
      const newEntity = await prisma.entity.upsert({
        where: { name: restOfEntityData.name },
        update: {
          ...restOfEntityData,
          submitterId: submitter.id,
        },
        create: {
          ...restOfEntityData,
          submitterId: submitter.id,
        },
      });

      // 2. Upsert related details
      if (details) {
        if (details.entityDetailsTool) {
          await prisma.entityDetailsTool.upsert({
            where: { entityId: newEntity.id },
            update: { ...details.entityDetailsTool },
            create: { ...details.entityDetailsTool, entityId: newEntity.id }
          });
        }
        if (details.entityDetailsCourse) {
          await prisma.entityDetailsCourse.upsert({
            where: { entityId: newEntity.id },
            update: { ...details.entityDetailsCourse },
            create: { ...details.entityDetailsCourse, entityId: newEntity.id }
          });
        }
        if (details.entityDetailsResearchPaper) {
          await prisma.entityDetailsResearchPaper.upsert({
            where: { entityId: newEntity.id },
            update: { ...details.entityDetailsResearchPaper },
            create: { ...details.entityDetailsResearchPaper, entityId: newEntity.id }
          });
        }
        if (details.entityDetailsPlatform) {
          await prisma.entityDetailsPlatform.upsert({
            where: { entityId: newEntity.id },
            update: { ...details.entityDetailsPlatform },
            create: { ...details.entityDetailsPlatform, entityId: newEntity.id }
          });
        }
        if (details.entityDetailsNewsletter) {
          await prisma.entityDetailsNewsletter.upsert({
            where: { entityId: newEntity.id },
            update: { ...details.entityDetailsNewsletter },
            create: { ...details.entityDetailsNewsletter, entityId: newEntity.id }
          });
        }
        if (details.entityDetailsEvent) {
          await prisma.entityDetailsEvent.upsert({
            where: { entityId: newEntity.id },
            update: { ...details.entityDetailsEvent },
            create: { ...details.entityDetailsEvent, entityId: newEntity.id }
          });
        }
      }

      // 3. Link categories, tags, features (many-to-many) - skip if already exists
      if (categoryIds && categoryIds.length > 0) {
        for (const categoryId of categoryIds) {
          await prisma.entityCategory.upsert({
            where: { entityId_categoryId: { entityId: newEntity.id, categoryId } },
            update: {},
            create: { entityId: newEntity.id, categoryId, assignedBy: submitter.id }
          });
        }
      }
      if (tagIds && tagIds.length > 0) {
        for (const tagId of tagIds) {
          await prisma.entityTag.upsert({
            where: { entityId_tagId: { entityId: newEntity.id, tagId } },
            update: {},
            create: { entityId: newEntity.id, tagId, assignedBy: submitter.id }
          });
        }
      }
      if (featureIds && featureIds.length > 0) {
        for (const featureId of featureIds) {
          await prisma.entityFeature.upsert({
            where: { entityId_featureId: { entityId: newEntity.id, featureId } },
            update: {},
            create: { entityId: newEntity.id, featureId, assignedBy: submitter.id }
          });
        }
      }

      console.log(`Seeded entity: ${newEntity.name}`);
    }
  } else {
    console.log('Skipping entity seeding due to missing prerequisites (submitter, entity types, etc.).');
  }

  console.log(`Seeding finished.`);
}

main()
  .catch(async (e) => {
    console.error(e);
    await prisma.$disconnect();
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 